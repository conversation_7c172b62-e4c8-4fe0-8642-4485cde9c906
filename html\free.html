<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>免费资源网站 - 线路状态</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  
  <!-- 配置Tailwind自定义颜色和字体 -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#165DFF',
            success: '#10B981',
            warning: '#F59E0B',
            danger: '#EF4444',
            dark: '#1E293B',
            light: '#F8FAFC'
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  
  <style type="text/tailwindcss">
    @layer utilities {
      .card-shadow {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      }
      .card-hover {
        transition: all 0.3s ease;
      }
      .card-hover:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
      }
      .status-pulse {
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
      }
      @keyframes pulse {
        0%, 100% {
          opacity: 1;
        }
        50% {
          opacity: 0.7;
        }
      }
    }
  </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen font-inter text-dark">
  <!-- 顶部导航 -->
  <header class="bg-white shadow-md sticky top-0 z-50 transition-all duration-300">
    <div class="container mx-auto px-4 py-4 flex justify-between items-center">
      <div class="flex items-center space-x-2">
        <i class="fa fa-play-circle text-primary text-2xl"></i>
        <h1 class="text-[clamp(1.25rem,3vw,1.75rem)] font-bold text-primary">免费资源网站</h1>
      </div>
      <div class="flex items-center space-x-4">
        <span id="last-checked" class="text-sm text-gray-500 hidden md:inline-block">
          <i class="fa fa-refresh mr-1"></i>
          <span>最后检查: 从未</span>
        </span>
        <button id="refresh-btn" class="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-lg flex items-center transition-all">
          <i class="fa fa-refresh mr-2"></i>
          <span>刷新状态</span>
        </button>
      </div>
    </div>
  </header>

  <!-- 主内容区 -->
  <main class="container mx-auto px-4 py-8">
    <!-- 介绍部分 -->
    <div class="text-center mb-10 max-w-3xl mx-auto">
      <h2 class="text-[clamp(1.5rem,3vw,2rem)] font-bold mb-4">流媒体资源访问</h2>
      <p class="text-gray-600 text-lg">以下是可用的流媒体资源站点，每个站点提供多条访问线路。绿色标识表示线路可正常访问，红色表示线路暂时不可用。</p>
    </div>

    <!-- 站点卡片容器 -->
    <div id="sites-container" class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
      <!-- 站点将通过JavaScript动态生成 -->
    </div>
    
    <!-- 使用说明 -->
    <div class="bg-white rounded-xl shadow-md p-6 max-w-3xl mx-auto">
      <h3 class="text-xl font-bold mb-4 flex items-center">
        <i class="fa fa-info-circle text-primary mr-2"></i>使用说明
      </h3>
      <ul class="space-y-3 text-gray-600">
        <li class="flex items-start">
          <i class="fa fa-check-circle text-success mt-1 mr-3"></i>
          <span>点击线路链接将在新窗口打开对应网站</span>
        </li>
        <li class="flex items-start">
          <i class="fa fa-check-circle text-success mt-1 mr-3"></i>
          <span>如果某条线路无法访问，请尝试其他线路</span>
        </li>
        <li class="flex items-start">
          <i class="fa fa-check-circle text-success mt-1 mr-3"></i>
          <span>点击"刷新状态"按钮可以重新检查所有线路的可用性</span>
        </li>
      </ul>
    </div>
  </main>

  <!-- 页脚 -->
  <footer class="bg-dark text-white mt-16 py-8">
    <div class="container mx-auto px-4 text-center">
      <p class="mb-4">免费资源网站导航</p>
      <p class="text-gray-400 text-sm">© 2023 资源导航页面 | 所有链接均为第三方提供</p>
    </div>
  </footer>

  <!-- 通知提示 -->
  <div id="notification" class="fixed bottom-6 right-6 bg-dark text-white px-6 py-3 rounded-lg shadow-lg transform translate-y-20 opacity-0 transition-all duration-300 flex items-center">
    <i class="fa fa-check-circle mr-2"></i>
    <span id="notification-text">操作成功</span>
  </div>

  <script>
    // 站点数据 - 所有站点信息都集中在此数组中管理
    const sites = [
      {
        name: "LibreTV",
        icon: "fa-television",
        links: [
          { url: "https://libretv-jsh40svv.edgeone.app/", platform: "EdgeOne" },
          { url: "https://libretv-3fh.pages.dev/", platform: "Cloudflare" },
          { url: "https://libretv.magiccode.dpdns.org/", platform: "MagicCode" }
        ]
      },
      {
        name: "MoonTV",
        icon: "fa-moon-o",
        links: [
          { url: "https://moontv-bnx.pages.dev/", platform: "Cloudflare" },
          { url: "https://moontv.magiccode.dpdns.org/", platform: "MagicCode" }
        ]
      }
    ];

    // DOM元素
    const refreshBtn = document.getElementById('refresh-btn');
    const lastCheckedEl = document.querySelector('#last-checked span');
    const notification = document.getElementById('notification');
    const notificationText = document.getElementById('notification-text');
    const sitesContainer = document.getElementById('sites-container');

    // 显示通知
    function showNotification(message) {
      notificationText.textContent = message;
      notification.classList.remove('translate-y-20', 'opacity-0');
      notification.classList.add('translate-y-0', 'opacity-100');
      
      setTimeout(() => {
        notification.classList.remove('translate-y-0', 'opacity-100');
        notification.classList.add('translate-y-20', 'opacity-0');
      }, 3000);
    }

    // 生成站点卡片HTML
    function generateSiteCards() {
      sitesContainer.innerHTML = '';
      
      sites.forEach(site => {
        const siteCard = document.createElement('div');
        siteCard.className = 'bg-white rounded-xl shadow-lg overflow-hidden card-hover';
        
        // 站点头部
        siteCard.innerHTML = `
          <div class="bg-primary/10 px-6 py-4 border-b border-primary/20">
            <div class="flex justify-between items-center">
              <h3 class="text-xl font-bold text-primary flex items-center">
                <i class="fa ${site.icon} mr-3 text-2xl"></i>
                ${site.name}
              </h3>
              <span class="bg-primary/20 text-primary px-3 py-1 rounded-full text-sm font-medium">
                ${site.links.length}条线路
              </span>
            </div>
          </div>
          
          <div class="p-6">
            <!-- 线路列表 -->
            <div>
              <h4 class="font-semibold text-gray-700 mb-4">可用线路</h4>
              <ul class="space-y-4" id="${site.name.toLowerCase()}-links">
                ${site.links.map((link, index) => `
                  <li class="link-item">
                    <div class="flex flex-col sm:flex-row sm:items-center justify-between bg-gray-50 p-4 rounded-lg border border-gray-200 hover:border-primary/30 transition-all">
                      <div class="mb-2 sm:mb-0">
                        <div class="font-medium mb-1">线路${index + 1} (${link.platform})</div>
                        <a href="${link.url}" target="_blank" class="text-primary hover:underline text-sm break-all">
                          ${link.url}
                        </a>
                      </div>
                      <div class="status-indicator flex items-center">
                        <span class="w-3 h-3 rounded-full bg-gray-400 mr-2 status-pulse"></span>
                        <span class="text-sm">检查中...</span>
                      </div>
                    </div>
                  </li>
                `).join('')}
              </ul>
            </div>
          </div>
        `;
        
        sitesContainer.appendChild(siteCard);
      });
    }

    // 检测链接状态
    async function checkLinkStatus(url) {
      try {
        // 使用HEAD请求检查状态，避免获取整个页面内容
        const response = await fetch(url, { method: 'HEAD', mode: 'no-cors', cache: 'no-store' });
        return response.ok; // 如果状态码在200-299之间，返回true
      } catch (error) {
        // 网络错误或其他异常
        return false;
      }
    }

    // 更新所有链接状态
    async function updateLinkStatuses() {
      // 显示刷新中状态
      refreshBtn.disabled = true;
      refreshBtn.innerHTML = '<i class="fa fa-spinner fa-spin mr-2"></i><span>刷新中...</span>';
      
      // 更新最后检查时间
      const now = new Date();
      const formattedTime = now.toLocaleString('zh-CN');
      lastCheckedEl.textContent = `最后检查: ${formattedTime}`;
      
      // 为每个站点的每个链接检查状态
      for (const site of sites) {
        const linksContainer = document.getElementById(`${site.name.toLowerCase()}-links`);
        
        for (let i = 0; i < site.links.length; i++) {
          const linkItem = linksContainer.querySelector(`.link-item:nth-child(${i + 1})`);
          const statusIndicator = linkItem.querySelector('.status-indicator');
          const statusDot = statusIndicator.querySelector('span:first-child');
          const statusText = statusIndicator.querySelector('span:last-child');
          
          // 重置状态显示
          statusDot.className = 'w-3 h-3 rounded-full bg-gray-400 mr-2 status-pulse';
          statusText.textContent = '检查中...';
          
          // 检查链接状态
          const isOnline = await checkLinkStatus(site.links[i].url);
          
          // 更新状态显示
          if (isOnline) {
            statusDot.className = 'w-3 h-3 rounded-full bg-success mr-2';
            statusText.textContent = '在线';
            statusText.className = 'text-sm text-success';
          } else {
            statusDot.className = 'w-3 h-3 rounded-full bg-danger mr-2';
            statusText.textContent = '离线';
            statusText.className = 'text-sm text-danger';
          }
        }
      }
      
      // 恢复刷新按钮
      refreshBtn.disabled = false;
      refreshBtn.innerHTML = '<i class="fa fa-refresh mr-2"></i><span>刷新状态</span>';
      showNotification('所有线路状态已更新');
    }

    // 初始化页面
    function init() {
      // 生成站点卡片
      generateSiteCards();
      
      // 添加刷新按钮事件监听
      refreshBtn.addEventListener('click', updateLinkStatuses);
      
      // 页面加载完成后自动检查一次状态
      setTimeout(updateLinkStatuses, 1000);
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', init);
  </script>
</body>
</html>