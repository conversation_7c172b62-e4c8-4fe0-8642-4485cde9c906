// ==UserScript==
// @name        修正鼠标前后键在飞牛控制台文件管理中的效果
// @namespace   nixingshiguang
// @version     1.0
// @description 修改飞牛网页控制台中，打开文件管理后，鼠标前进后退键会触发浏览器的行为\n修改前：网页前进后退\n修改后，可以实现文件夹的前进和后退.
// <AUTHOR>
// @match       https://**********:8001/*
// @grant       none
// @supportURL  https://blog.160621.xyz
// ==/UserScript==

(function () {
    'use strict';

    const scriptTag = "%c[飞牛鼠标修正]%c";
    const styleTag = "color: white; background-color: #4CAF50; font-weight: bold;";
    const resetStyle = "color: inherit; background-color: inherit;";

    // 前进后退按钮组选择器
    const buttonsSelector = '.semi-button-split';

    // 创建一个通用函数处理后退操作
    function handleBackAction() {
        console.log(scriptTag + " 按下：后退键", styleTag, resetStyle);
        const backButtonElement = document.querySelector(buttonsSelector).firstChild;
        if (backButtonElement) {
            console.log(scriptTag + " 尝试点击：后退按钮元素", styleTag, resetStyle, backButtonElement);
            backButtonElement.click();
            return true;
        } else {
            console.log(scriptTag + " %c未找到：后退按钮元素%c", styleTag, "color: red;", resetStyle);
            return false;
        }
    }

    // 创建一个通用函数处理前进操作
    function handleForwardAction() {
        console.log(scriptTag + " 按下：前进键", styleTag, resetStyle);
        const forwardButtonElement = document.querySelector(buttonsSelector).lastChild;
        if (forwardButtonElement) {
            console.log(scriptTag + " 尝试点击：前进按钮元素", styleTag, resetStyle, forwardButtonElement);
            forwardButtonElement.click();
            return true;
        } else {
            console.log(scriptTag + " %c未找到：前进按钮元素%c", styleTag, "color: red;", resetStyle);
            return false;
        }
    }

    // 创建一个通用函数处理删除操作
    function handleDeleteAction() {
        console.log(scriptTag + " 按下：删除键", styleTag, resetStyle);
        const deleteButtonElement = document.querySelectorAll('span.semi-button-content-right')[3];
        if (deleteButtonElement.textContent.trim() === '删除' && ) {
            console.log(scriptTag + " 尝试点击：删除按钮元素", styleTag, resetStyle, deleteButtonElement);
            deleteButtonElement.click();
            return true;
        } else {
            console.log(scriptTag + " %c未找到：删除按钮元素%c", styleTag, "color: red;", resetStyle);
            return false;
        }
    }

    document.addEventListener('mousedown', function (event) {
        if (event.button === 3) { // 后退按钮
            if (handleBackAction()) {
                event.preventDefault();
                event.stopPropagation();
                return false;
            }
        } else if (event.button === 4) { // 前进按钮
            if (handleForwardAction()) {
                event.preventDefault();
                event.stopPropagation();
                return false;
            }
        }
    }, true); // 在捕获阶段监听

    document.addEventListener('mouseup', function (event) {
        if (event.button === 3) {
            console.log(scriptTag + " 释放：后退键", styleTag, resetStyle);
            event.preventDefault();
            event.stopPropagation();
            return false;
        } else if (event.button === 4) {
            console.log(scriptTag + " 释放：前进键", styleTag, resetStyle);
            event.preventDefault();
            event.stopPropagation();
            return false;
        }
    }, true); // 在捕获阶段监听

    document.addEventListener('keydown', function (event) {
        switch (event.key) {
            case 'Backspace': // 后退按钮
                if (handleBackAction()) {
                    event.preventDefault();
                    event.stopPropagation();
                    return false;
                }
                break;
            case 'ArrowRight': // 前进按钮 (Alt+右箭头)
                if (event.altKey && handleForwardAction()) {
                    event.preventDefault();
                    event.stopPropagation();
                    return false;
                }
                break;
            case 'Delete': // 删除按钮
                if (handleDeleteAction()) {
                    event.preventDefault();
                    event.stopPropagation();
                    return false;
                }
                break;
            default:
                return false;
        }
    }, true); // 在捕获阶段监听
})();
