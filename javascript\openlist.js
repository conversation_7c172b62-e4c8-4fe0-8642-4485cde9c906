// 禁止使用微信内置浏览器打开
function wechatBlock() {
    var ua = navigator.userAgent.toLowerCase();
    var isWeixin = ua.indexOf('micromessenger') != -1;
    var ua = navigator.userAgent;
    var ipad = ua.match(/(iPad).*OS\s([\d_]+)/),
        isIphone = !ipad && ua.match(/(iPhone\sOS)\s([\d_]+)/),
        isAndroid = ua.match(/(Android)\s+([\d.]+)/),
        isMobile = isIphone || isAndroid;
    if (isMobile) {
        if (isWeixin) {
            document.head.innerHTML = '<title>请用外部浏览器打开</title><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=0"><link rel="stylesheet" type="text/css" href="https://res.wx.qq.com/open/libs/weui/0.4.1/weui.css">';
            document.body.innerHTML = '<div class="weui_msg"><div class="weui_icon_area"><i class="weui_icon_info weui_icon_msg"></i></div><div class="weui_text_area"><h4 class="weui_msg_title">为防止被麻花藤拉黑，请点击右上角“...”，并使用浏览器打开</h4></div></div>';
        }
    }
};

// 禁用右键
function disableRightClick() {
    document.addEventListener('contextmenu', function (e) {
        e.preventDefault();
    }, false);
}

// 2秒后可点击头像跳转到后台
function goToDashboard() {
    setTimeout(() => {
        document.querySelector(".header-left").addEventListener("click", () => {
            window.open("/@manage", "_blank");
        })
    }, 2000)
}

// 使用壁纸
function wallpaper() {
    // 双击看壁纸
    // 获取id为root的元素
    var rootElement = document.getElementById('root');

    // 为整个文档添加双击事件监听器
    document.addEventListener('dblclick', function (event) {
        // 检查root元素当前是否可见
        if (rootElement.style.display === 'none') {
            // 如果不可见，则显示它
            rootElement.style.display = 'flex';
        } else {
            // 如果可见，则隐藏它
            rootElement.style.display = 'none';
        }
    });

    // 加载壁纸css文件
    var link = document.createElement('link');
    link.rel ='stylesheet';
    link.href = 'https://api.160621.xyz/static/openlist/wallpaper.css';
    document.head.appendChild(link);
}

// 网站运行时间
function websiteRunTime() {

    // 定义开始时间（2021年10月1日00:00:00）
    const startDate = new Date("2021-10-01T00:00:00");
    let timeInterval; // 存储时间间隔ID

    /**
     * 创建时间显示元素并添加到页面
     */
    function createTimeDisplayElements() {
        // 创建容器 div
        const container = document.createElement("div");
        container.className = "nav-item";
        container.id = "nav-item";

        // 创建时间显示元素
        const hyySpan = document.createElement("span");
        hyySpan.id = "hyy";

        const fkipSpan = document.createElement("span");
        fkipSpan.id = "fkip";

        const timeDateSpan = document.createElement("span");
        timeDateSpan.id = "timeDate";
        timeDateSpan.textContent = "载入天数...";

        const timesSpan = document.createElement("span");
        timesSpan.id = "times";

        // 将所有元素添加到容器
        container.appendChild(hyySpan);
        container.appendChild(fkipSpan);
        container.appendChild(timeDateSpan);
        container.appendChild(timesSpan);

        // 将容器添加到 body 末尾
        document.body.appendChild(container);

        // 应用样式
        applyStyles();
    }

    /**
     * 应用样式
     */
    function applyStyles() {
        const style = document.createElement("style");
        style.textContent = `
            #nav-item {
                width: 100vw;
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
                position: fixed;
                bottom: 10px;
                z-index: -1;
            }

            .hope-ui-light #nav-item span {
                backdrop-filter: blur(10px);
                background-color: rgba(255, 255, 255, 0.5);
                padding: 0px 10px;
                border-radius: 10px;
            }

            .hope-ui-dark #nav-item span {
                backdrop-filter: blur(10px);
                background-color: rgba(0, 0, 0, 0.5);
                padding: 0px 10px;
                border-radius: 10px;
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * 更新时间显示
     */
    function updateTimeDisplay() {
        const now = new Date();
        now.setTime(now.getTime() + 250); // 增加250毫秒，与原代码保持一致

        // 计算时间差
        const timeDiff = now - startDate;
        const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

        // 格式化显示
        const formattedHours = String(hours).padStart(2, '0');
        const formattedMinutes = String(minutes).padStart(2, '0');
        const formattedSeconds = String(seconds).padStart(2, '0');

        // 更新DOM
        const timeDateEl = document.getElementById("timeDate");
        const timesEl = document.getElementById("times");

        if (timeDateEl && timesEl) {
            timeDateEl.innerHTML = "⏱️本站已运行" + days + "天";
            timesEl.innerHTML = formattedHours + "小时" + formattedMinutes + "分" + formattedSeconds + "秒";
        }
    }

    /**
     * 控制时间计数器的启动和关闭
     * @param {boolean} isStart - 是否启动计数器
     */
    function controlTimeCounter(isStart) {
        if (isStart) {
            // 启动计数器
            timeInterval = setInterval(updateTimeDisplay, 250);
            console.log("时间计数器已启动");
        } else {
            // 关闭计数器
            clearInterval(timeInterval);
            console.log("时间计数器已关闭");
        }
    }

    /**
     * 切换时间计数器状态
     */
    function toggleTimeCounter() {
        startTime = !startTime;
        controlTimeCounter(startTime);
    }

    /**
     * 初始化时间计数器
     */
    function initTimeCounter() {
        // 创建DOM元素
        createTimeDisplayElements();

        // 初始调用一次，避免首次显示延迟
        updateTimeDisplay();

        // 根据startTime状态决定是否启动计时器
        controlTimeCounter(startTime);
    }
    initTimeCounter();
}

// 在html加载完之后再执行以下的代码
window.addEventListener('load', () => {
    if (isWechatBlock) {
        wechatBlock();
    }
    if (isRightClickDisable) {
        disableRightClick();
    }
    goToDashboard();
    if (isWallpaper) {
        wallpaper();
    }
    if (startTime) {
        websiteRunTime();
    }
})