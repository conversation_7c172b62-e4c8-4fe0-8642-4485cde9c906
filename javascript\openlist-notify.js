// 设置 webhook url
const webhookUrlOpenlist = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=efd47e0f-5771-43f4-863f-be16be638921';

async function notify(message) {
    // 使用fetch API发送请求
    fetch(webhookUrlOpenlist, {
        mode: 'no-cors',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(message)
    }).then(() => { });
}

// 给body添加事件委托
document.body.addEventListener('click', function (event) {
    console.log("点击元素：" + event.target.tagName);
    // 检查点击的目标是否是包含“下载”文本的a元素
    if (event.target.tagName === 'A' && event.target.textContent.includes('下载')) {
        // 获取用户信息
        user_from = document.querySelector("#hyy").innerHTML.slice(4);

        // 获取下载地址
        download_url = decodeURI(window.location.pathname);
        // download_url = decodeURI(event.target.href);

        // 构建要发送的消息内容
        const message = {
            msgtype: 'markdown',
            markdown: {
                content: `来自<font color="warning">${user_from}</font>开始下载文件

><font color="comment">下载链接：</font>${download_url}`
            }
        };

        // 使用fetch API发送请求
        notify(message);
    }

    // 检查点击的目标是否是包含“复制链接”文本的a元素
    if (event.target.tagName === 'BUTTON' && event.target.textContent.includes('复制链接')) {
        // 获取用户信息
        user_from = document.querySelector("#hyy").innerHTML.slice(4);

        // 获取下载地址
        download_url = decodeURI(event.target.nextElementSibling.href);
        download_file = decodeURI(window.location.pathname);

        const message = {
            msgtype: 'markdown',
            markdown: {
                content: `${user_from}

><font color="comment">复制的链接：</font>[${download_file}](${download_url})`
            }
        };
        notify(message);

        // 生成一个距离当前时间七天的单位为秒的时间错
        function expireTime() {
            const now = new Date().getTime();
            const expireTime = now + 7 * 24 * 60 * 60 * 1000;
            return expireTime;
        }

        // 发送请求转换短链
        const response = fetch("https://sink.160621.xyz/api/link/create", {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'authorization': 'Bearer qE890mMTvZ^RFbbyxhreyk1YvTrNyb57'
            },
            body: JSON.stringify({
                url: "https://blog.160621.xyz/",
                expiration: expireTime()
            })
        });
        response.then(response => response.json())
            .then(data => {
                console.log("短链API返回的数据：" + JSON.stringify(data, null, 2))
                const short_url = data.shortLink;
                console.log("生成的短链为：" + short_url)
                // 把短链复制到剪贴板
                navigator.clipboard.writeText(short_url)
            })
    }
});