#!/bin/bash

# 检查操作系统是否为Windows
if [[ "$OSTYPE" != "cygwin" && "$OSTYPE" != "msys" && "$OSTYPE" != "win32" ]]; then
    echo "警告：此脚本主要设计用于在 Windows 环境下的 Bash（例如 Git Bash, WSL, Cygwin）中运行。"
    echo "在其他操作系统上可能会有不同的表现或需要调整。"
fi

echo "--------------------------------------------------------"
echo "  TXT 文件字符串删除工具"
echo "--------------------------------------------------------"

# 提示用户输入文件夹路径
read -p "请输入要遍历的文件夹路径 (例如: C:\\Users\\<USER>\\Documents): " folder_path

# 转换路径格式以适应Bash环境（如果需要的话，例如从Windows路径转为WSL路径）
# 如果你是在Git Bash或Cygwin中运行，直接使用Windows路径格式通常是可以的。
# 如果你是在WSL中运行，并且路径是Windows盘符开头的，可能需要转换，例如：
# /mnt/c/Users/<USER>/Documents
# 这里我们假设用户输入的是Bash可以直接识别的路径，或者在Git Bash/Cygwin下直接使用Windows路径。

# 检查文件夹是否存在
if [ ! -d "$folder_path" ]; then
    echo "错误：指定的文件夹不存在或无法访问。"
    exit 1
fi

# 提示用户输入要删除的字符串
read -p "请输入要删除的字符串 (例如: /分享): " string_to_delete

# 确认操作
read -p "您确定要删除 '$string_to_delete' 字符串在 '$folder_path' 文件夹中所有txt文件内吗? (y/n): " confirm
if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
    echo "操作已取消。"
    exit 0
fi

echo "开始处理文件..."

# 遍历文件夹中的所有 .txt 文件
# 这里使用 find 命令更健壮，可以处理子目录
find "$folder_path" -type f -iname "*.txt" | while IFS= read -r file; do
    echo "正在处理文件: $file"

    # 使用 sed 命令进行原地替换
    # -i 选项表示原地编辑文件
    # s/旧字符串/新字符串/g 表示全局替换
    # 为了处理包含特殊字符的字符串，需要对 sed 命令的定界符进行选择，这里使用 |
    # 对于字符串中的特殊字符（如 /），如果直接在 sed 表达式中使用 / 作为定界符，需要进行转义。
    # 更好的做法是使用不同的定界符，例如 # 或 @。
    # 让我们选择 @ 作为定界符，这样用户输入的字符串中的 / 就不需要转义了。
    # sed -i "s@$string_to_delete@@g" "$file"

    # 考虑到用户输入的字符串可能包含sed的特殊字符（如/, &, \等），
    # 最安全的做法是对其进行转义。
    # 但是，如果只是简单的字符串替换，并且用户输入的字符串不包含这些特殊字符，
    # 使用 @ 作为分隔符通常是足够的。
    # 如果用户输入的字符串可能包含特殊字符，例如正则表达式的元字符，那么需要更复杂的转义。
    # 对于本例中删除字面量字符串的需求，sed 's@STRING_TO_DELETE@@g' 是一个好选择。
    # 唯一需要注意的是，如果用户输入的字符串本身包含 @ 字符，那么这个定界符就不合适了。
    # 一个更通用的方法是使用 printf "%s\n" "$string_to_delete" | sed 's/[][\/.^$*+?(){}|-]/\\&/g' | tr -d '\n' 来转义字符串，但对于简单需求过于复杂。

    # 最直接且对用户输入友好，同时避免定界符冲突的方法是使用 sed 传递替换字符串。
    # 但是 sed -i 选项的实现可能因系统而异 (GNU sed vs BSD sed)
    # 对于跨平台兼容性，更稳健的方法是：
    # 1. 读取文件内容
    # 2. 替换内容
    # 3. 写入临时文件
    # 4. 替换原文件

    # 临时文件
    temp_file="${file}.tmp"

    # 读取文件内容，进行替换，并写入临时文件
    # 使用 grep -q 判断是否存在，避免不必要的替换和文件写入
    if grep -q "$string_to_delete" "$file"; then
        sed "s|$string_to_delete||g" "$file" > "$temp_file"
        if [ $? -eq 0 ]; then # 检查 sed 命令是否成功
            mv "$temp_file" "$file"
            echo "  - 已处理并保存。"
        else
            echo "  - 错误：处理文件失败。"
            rm -f "$temp_file" # 删除临时文件
        fi
    else
        echo "  - 文件中没有找到 '$string_to_delete'，跳过。"
    fi

done

echo "--------------------------------------------------------"
echo "所有TXT文件处理完成。"
echo "--------------------------------------------------------"