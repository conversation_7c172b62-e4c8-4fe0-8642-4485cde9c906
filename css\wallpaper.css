




/*白天背景图*/
.hope-ui-light {
    /* background-image: url("https://api.160621.xyz/img/ai/index.php") !important; */
    background-repeat: no-repeat;
    background-size: cover;
    background-attachment: fixed;
    background-position-x: center;
}

/*夜间背景图*/
.hope-ui-dark {
    /* background-image: url("https://api.160621.xyz/img/ai/index.php") !important; */
    background-repeat: no-repeat;
    background-size: cover;
    background-attachment: fixed;
    background-position-x: center;
    /* backdrop-filter: brightness(0.8); */
}

/*主列表白天模式透明*/
.obj-box.hope-stack.hope-c-dhzjXW.hope-c-PJLV.hope-c-PJLV-igScBhH-css {
    background-color: rgba(255, 255, 255, 0.5) !important;
}

/*主列表夜间模式透明*/
.obj-box.hope-stack.hope-c-dhzjXW.hope-c-PJLV.hope-c-PJLV-iigjoxS-css {
    background-color: rgb(0 0 0 / 50%) !important;
}

/*readme白天模式透明*/
.hope-c-PJLV.hope-c-PJLV-ikSuVsl-css {
    background-color: rgba(255, 255, 255, 0.5) !important;
}

/*readme夜间模式透明*/
.hope-c-PJLV.hope-c-PJLV-iiuDLME-css {
    background-color: rgb(0 0 0 / 50%) !important;
}

/*顶部右上角切换按钮透明*/
.hope-ui-light .hope-c-ivMHWx-hZistB-cv.hope-icon-button {
    background-color: rgba(255, 255, 255, 0.5) !important;
}

.hope-ui-dark .hope-c-ivMHWx-hZistB-cv.hope-icon-button {
    background-color: rgb(0 0 0 / 50%) !important;
}

/*右下角侧边栏按钮透明 第一个是白天 第二个是夜间*/
.hope-ui-light .hope-c-PJLV-ijgzmFG-css {
    background-color: rgba(255, 255, 255, 0.5) !important;
}

.hope-ui-dark .hope-c-PJLV-ijgzmFG-css {
    background-color: rgb(0 0 0 / 50%) !important;
}

/*白天模式代码块透明*/
.hope-ui-light pre {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

/*夜间模式代码块透明*/
.hope-ui-dark pre {
    background-color: rgba(255, 255, 255, 0) !important;
}

/*正常情况未使用吸附功能*/
/*顶部*/
.hope-c-PJLV-ikaMhsQ-css {
    background: rgba(255, 255, 255, 0) !important;
}

/*导航条*/
/*白天模式*/
.hope-ui-light .hope-c-PJLV-idaeksS-css li {
    background-color: rgba(255, 255, 255, 0.5) !important;
    border-radius: var(--hope-radii-xl) !important;
}

.hope-c-PJLV-idaeksS-css {
    background-color: transparent !important;
}

/*夜间模式*/
.hope-ui-dark .hope-c-PJLV-idaeksS-css li {
    background-color: rgb(0 0 0 / 50%) !important;
    border-radius: var(--hope-radii-xl) !important;
}

.hope-c-PJLV-idaeksS-css {
    background-color: transparent !important;
}

/* 吸附到页面顶部 */
/*顶部*/
.hope-c-PJLV-icWrYmg-css {
    background: rgba(255, 255, 255, 0) !important;
}

/*导航条*/
.hope-c-PJLV-icKsjdm-css::after {
    background: transparent !important;
}

/*白天模式*/
.hope-ui-light .hope-c-PJLV-icKsjdm-css li {
    background-color: rgba(255, 255, 255, 0.5) !important;
    border-radius: var(--hope-radii-xl) !important;
    backdrop-filter: blur(10px);
}

.hope-c-PJLV-icKsjdm-css {
    background-color: transparent !important;
}

/*夜间模式*/
.hope-ui-dark .hope-c-PJLV-icKsjdm-css li {
    background-color: rgb(0 0 0 / 50%) !important;
    border-radius: var(--hope-radii-xl) !important;
    backdrop-filter: blur(10px);
}

.hope-c-PJLV-icKsjdm-css {
    background-color: transparent !important;
}

/*仅吸附导航栏*/
/*导航条*/
.hope-c-PJLV-ifdXShc-css::after {
    background: rgba(255, 255, 255, 0) !important;
}

/*白天模式*/
.hope-ui-light .hope-c-hrsMRY li {
    background-color: rgba(255, 255, 255, 0.5) !important;
    border-radius: var(--hope-radii-xl) !important;
}

.hope-c-hrsMRY {
    background-color: transparent !important;
}

/*夜间模式*/
.hope-ui-dark .hope-c-hrsMRY li {
    background-color: rgb(0 0 0 / 50%) !important;
    border-radius: var(--hope-radii-xl) !important;
}

.hope-c-hrsMRY {
    background-color: transparent !important;
}

/* 搜索栏 */
/*白天模式 搜索主体+毛玻璃*/
.hope-ui-light .hope-c-PJLV-iiBaxsN-css {
    background-color: rgba(255, 255, 255, 0.2) !important;
    backdrop-filter: blur(10px) !important;
}

/*白天模式 搜索栏输入框+毛玻璃*/
.hope-ui-light .hope-c-kvTTWD-hYRNAb-variant-filled {
    background-color: rgba(255, 255, 255, 0.2) !important;
    backdrop-filter: blur(10px) !important;
}

/*白天模式 搜索按钮+毛玻璃*/
.hope-ui-light .hope-c-PJLV-ikEIIxw-css {
    background-color: rgba(255, 255, 255, 0.2) !important;
    backdrop-filter: blur(10px) !important;
    padding: var(--hope-space-1) !important;
}

/*夜间模式搜索主体+毛玻璃*/
.hope-ui-dark .hope-c-PJLV-iiBaxsN-css {
    background-color: rgb(0 0 0 / 10%) !important;
    backdrop-filter: blur(10px) !important;
}

/*夜间模式搜索栏+毛玻璃*/
.hope-ui-dark .hope-c-kvTTWD-hYRNAb-variant-filled {
    background-color: rgb(0 0 0 / 10%) !important;
    backdrop-filter: blur(10px) !important;
}

/*夜间模式 搜索按钮+毛玻璃*/
.hope-ui-dark .hope-c-PJLV-ikEIIxw-css {
    background-color: rgb(0 0 0 / 10%) !important;
    backdrop-filter: blur(10px) !important;
    padding: var(--hope-space-1) !important;
}

