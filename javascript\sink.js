// 生成一个距离当前时间七天的单位为秒的时间错
function expireTime() {
    const now = new Date().getTime();
    const expireTime = now + 7 * 24 * 60 * 60 * 1000;
    return expireTime;
}

// 向服务器发送请求，生成一个新的sink链接

(function () {
    fetch('https://sink.160621.xyz/api/link/create',
        {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'authorization': 'Bearer qE890mMTvZ^RFbbyxhreyk1YvTrNyb57'
            },
            body: JSON.stringify({
                url: "https://blog.160621.xyz/",
                expiration: expireTime()
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log(data);
        })
        .catch(error => {
            console.error(error);
        });
})();