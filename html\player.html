<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Video Player</title>
    <style>
        body,
        html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #141414;
        }

        video {
            width: 100%;
            height: 100%;
        }
    </style>
</head>

<body>
    <video id="video" controls controlsList="nodownload"></video>

    <script>
        const videoSrc = window.location.search.substring(1); // 移除问号
        if (videoSrc) {
            const videoElement = document.getElementById('video');
            videoElement.src = decodeURIComponent(videoSrc); // 以防有特殊字符，先进行解码
        } else {
            console.error('查询字符串中没有包含视频地址');
        }

        function setWindowTitleFromURL() {
            // 获取当前页面的URL
            const currentURL = window.location.href;
            // 解析查询字符串
            const queryString = currentURL.split('?')[1];
            // 从查询字符串中获取文件URL
            const fileUrl = decodeURIComponent(queryString.split('&')[0]);
            // 解析文件URL以获取文件名
            const filenameWithExtension = fileUrl.split('/').pop();
            // 移除文件扩展名
            const filename = filenameWithExtension.split('.').slice(0, -1).join('.');
            // 设置网页标题
            document.title = filename;
        }

        // 调用函数以设置网页标题
        window.onload = function () {
            setWindowTitleFromURL();
        }

    </script>
    <!-- 网站访问通知 -->
    <script type="text/javascript" src="https://api.160621.xyz/static/notify/custom.js" defer></script>
</body>

</html>