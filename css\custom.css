/*取消设置body高度*/
html,
body {
    height: unset !important;
    min-height: 100vh;
}

/*取消设置列表的最小高度*/
.hope-c-PJLV-iiHckfM-css {
    min-height: unset !important;
}

/*  隐藏滚动条 */
::-webkit-scrollbar {
    display: none;
}
/*禁止选择文本*/
body {
    user-select: none;
}

/* 隐藏页脚 */
.footer {
    display: none !important;
}
/* 登录界面 */
/* 隐藏默认背景 */
.hope-c-PJLV-ihWgyFw-css {
    display: none !important;
}

/* 登录模块 */
.hope-c-PJLV-ifjOQLV-css {
    background-color: rgba(255, 255, 255, 0.6) !important;
    backdrop-filter: blur(10px);
}

/* 账号密码输入框 */
.hope-c-PJLV-ibtHApG-css {
    background-color: transparent !important;
    border: 1px solid white !important;
}

/* 登录界面结束 */

/* 针对 AList 中 README 文件的预览区域的一级标题（H1）居中显示 */
.markdown-body h1 {
    text-align: center; /* 将文本水平居中 */
    max-width: 100%;  /* 限制最大宽度，让标题内容不会铺满整个屏幕 */
    margin-left: auto; /* 配合 max-width 实现块级元素居中 */
    margin-right: auto; /* 配合 max-width 实现块级元素居中 */
}