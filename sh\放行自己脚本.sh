#!/bin/sh

# 域名：nxsg.eu.org
echo "判断域名：nxsg.eu.org"
# 判断存储旧ip的文件是否存在，不存在则新建
if [ -f /tmp/iprecord1.txt ]; then
    echo "文件已存在"
else
    echo "文件不存在，自动新建"
    touch /tmp/iprecord1.txt
fi

# 获取新ip
new_ip1=$(dig AAAA nxsg.eu.org +short)
if [ -z "$new_ip1" ]; then
    echo "错误：无法获取IP地址，退出脚本"
    exit 1
fi
echo "新的IP为：$new_ip1"
# 获取ip前缀
cidr1=$(echo "$new_ip1" | cut -d ':' -f 1-4)::/64
echo "新ip前缀为：$cidr1"

# 获取旧前缀ip
old_cidr1=$(cat /tmp/iprecord1.txt)
echo "旧ip前缀为：$old_cidr1"

# 新旧ip前缀比较
if [ "$cidr1" = "$old_cidr1" ]; then
    echo "ip前缀没有变换"
else
    echo "ip前缀更新"
    echo "update ufw rules"
    ufw allow from "$cidr1" to any proto tcp port 32123
    ufw delete allow from "$old_cidr1" to any proto tcp port 32123
    ufw reload
    echo "$cidr1" > /tmp/iprecord1.txt
fi




